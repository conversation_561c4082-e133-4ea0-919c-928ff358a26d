# Default values for Tour de Cloud Keycloak deployment
# This is a YAML-formatted file.

# Global configuration
global:
  storageClass: ""
  # Namespace configuration
  namespace: "keycloak"

# Keycloak configuration
keycloak:
  enabled: true
  
  # Keycloak admin credentials
  auth:
    adminUser: admin
    # adminPassword: "" # Will be auto-generated if not set
    existingSecret: ""
    passwordSecretKey: ""
  
  # Keycloak configuration
  configuration: ""
  existingConfigmap: ""
  
  # Extra environment variables
  extraEnvVars: []

  # Extra environment variables from secrets
  extraEnvVarsSecret: ""
  
  # Keycloak service configuration
  service:
    type: ClusterIP
    ports:
      http: 80
      https: 443
    nodePorts:
      http: ""
      https: ""
    sessionAffinity: None
    sessionAffinityConfig: {}
    clusterIP: ""
    loadBalancerIP: ""
    loadBalancerSourceRanges: []
    externalTrafficPolicy: Cluster
    annotations: {}
    extraPorts: []
  
  # Ingress configuration
  ingress:
    enabled: false
    hostname: keycloak.local
    annotations: {}
    tls: false
    selfSigned: false
    extraHosts: []
    extraPaths: []
    extraTls: []
    secrets: []
    ingressClassName: ""
  
  # Production resources
  resources:
    limits:
      cpu: 2000m
      memory: 2Gi
    requests:
      cpu: 1000m
      memory: 1Gi

  # Multiple replicas for high availability
  replicaCount: 2

  # Enable pod disruption budget
  pdb:
    create: true
    minAvailable: 1

  # Enable autoscaling
  autoscaling:
    enabled: true
    minReplicas: 1
    maxReplicas: 2
    targetCPU: 70
    targetMemory: 80

  # Persistence
  persistence:
    enabled: true
    size: 20Gi
    storageClass: ""  # Use default storage class
  
  # PostgreSQL database configuration
  postgresql:
    enabled: true
    auth:
      postgresPassword: ""
      username: keycloak
      password: ""
      database: keycloak
      existingSecret: ""
      secretKeys:
        adminPasswordKey: postgres-password
        userPasswordKey: password
    architecture: standalone
    primary:
      persistence:
        enabled: true
        size: 8Gi
        storageClass: ""
      resources:
        limits:
          cpu: 500m
          memory: 512Mi
        requests:
          cpu: 250m
          memory: 256Mi

  # Keycloak startup and liveness probes
  livenessProbe:
    enabled: true
    initialDelaySeconds: 300
    periodSeconds: 1
    timeoutSeconds: 5
    failureThreshold: 3
    successThreshold: 1
  
  readinessProbe:
    enabled: true
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 1
    failureThreshold: 3
    successThreshold: 1
  
  # Security context
  podSecurityContext:
    enabled: true
    fsGroup: 1001
  
  containerSecurityContext:
    enabled: true
    runAsUser: 1001
    runAsNonRoot: true
    readOnlyRootFilesystem: false
  
  # Node affinity and tolerations
  nodeAffinityPreset:
    type: ""
    key: ""
    values: []
  
  affinity: {}
  nodeSelector: {}
  tolerations: []
  
  # Pod annotations and labels
  podAnnotations: {}
  podLabels: {}
  
  # Service account
  serviceAccount:
    create: true
    name: ""
    annotations: {}
    automountServiceAccountToken: true
  
  # RBAC
  rbac:
    create: false
    rules: []
  
  # Network policies
  networkPolicy:
    enabled: true
    allowExternal: true
    additionalRules:
      ingress: []
      egress: []

# GitHub OAuth configuration
# To enable GitHub as an identity provider:
# 1. Create a GitHub OAuth App at https://github.com/settings/applications/new
# 2. Set the Authorization callback URL to: https://your-keycloak-domain/realms/your-realm/broker/github/endpoint
# 3. Set clientId and clientSecret below, or use existingSecret for production
githubOAuth:
  enabled: false
  clientId: ""
  clientSecret: ""
  # Use existing secret instead of creating one (recommended for production)
  existingSecret: ""
  clientIdKey: "client-id"
  clientSecretKey: "client-secret"

# Custom configuration for Tour de Cloud
customConfig:
  # Custom realm configurations
  realms:
    # Default realm configuration - can be overridden in environment-specific files
    tourdecloud:
      realm: "tourdecloud"
      displayName: "Tour de Cloud"
      enabled: true
      sslRequired: "external"
      registrationAllowed: false
      loginWithEmailAllowed: true
      duplicateEmailsAllowed: false
      resetPasswordAllowed: true
      editUsernameAllowed: false
      bruteForceProtected: true
      permanentLockout: false
      maxFailureWaitSeconds: 900
      minimumQuickLoginWaitSeconds: 60
      waitIncrementSeconds: 60
      quickLoginCheckMilliSeconds: 1000
      maxDeltaTimeSeconds: 43200
      failureFactor: 30
      # Default password policy
      passwordPolicy: "length(8) and digits(1) and lowerCase(1) and upperCase(1) and specialChars(1) and notUsername"
      # Default token settings
      accessTokenLifespan: 300
      accessTokenLifespanForImplicitFlow: 900
      ssoSessionIdleTimeout: 1800
      ssoSessionMaxLifespan: 36000
      offlineSessionIdleTimeout: 2592000
      accessCodeLifespan: 60
      accessCodeLifespanUserAction: 300
      accessCodeLifespanLogin: 1800
      actionTokenGeneratedByAdminLifespan: 43200
      actionTokenGeneratedByUserLifespan: 300
      # Default clients configuration
      clients:
        - clientId: "tourdecloud-app"
          name: "Tour de Cloud Application"
          description: "Main application client for Tour de Cloud"
          enabled: true
          clientAuthenticatorType: "client-secret"
          standardFlowEnabled: true
          implicitFlowEnabled: false
          directAccessGrantsEnabled: false
          serviceAccountsEnabled: false
          publicClient: true
          frontchannelLogout: true
          protocol: "openid-connect"
          attributes:
            "access.token.lifespan": "300"
            "sso.session.idle.timeout": "1800"
            "sso.session.max.lifespan": "36000"
            "client.session.idle.timeout": "1800"
            "client.session.max.lifespan": "36000"
            "saml.assertion.signature": "false"
            "saml.force.post.binding": "false"
            "saml.multivalued.roles": "false"
            "saml.encrypt": "false"
            "saml.server.signature": "false"
            "saml.server.signature.keyinfo.ext": "false"
            "exclude.session.state.from.auth.response": "false"
            "saml_force_name_id_format": "false"
            "saml.client.signature": "false"
            "tls.client.certificate.bound.access.tokens": "false"
            "saml.authnstatement": "false"
            "display.on.consent.screen": "false"
            "saml.onetimeuse.condition": "false"
      # Default roles configuration
      roles:
        realm:
          - name: "user"
            description: "Standard user role"
          - name: "admin"
            description: "Administrator role"
        client:
          tourdecloud-app:
            - name: "app-user"
              description: "Application user"
            - name: "app-admin"
              description: "Application administrator"

  # Custom themes
  themes:
    # Default Tour de Cloud theme configuration
    tourdecloud:
      name: "tourdecloud"
      displayName: "Tour de Cloud Theme"
      # Theme properties for login pages
      properties:
        # Colors and branding
        primaryColor: "#2563eb"
        secondaryColor: "#1e40af"
        backgroundColor: "#f8fafc"
        textColor: "#1e293b"
        linkColor: "#2563eb"
        # Logo and branding
        logoUrl: "/auth/resources/tourdecloud/img/logo.png"
        logoAltText: "Tour de Cloud"
        faviconUrl: "/auth/resources/tourdecloud/img/favicon.ico"
        # Layout settings
        cardBackgroundColor: "#ffffff"
        cardBorderRadius: "8px"
        cardShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)"
        # Button styling
        buttonPrimaryBackground: "#2563eb"
        buttonPrimaryHover: "#1d4ed8"
        buttonSecondaryBackground: "#6b7280"
        buttonSecondaryHover: "#4b5563"
        # Form styling
        inputBorderColor: "#d1d5db"
        inputFocusBorderColor: "#2563eb"
        inputBackgroundColor: "#ffffff"
        # Typography
        fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
        fontSize: "14px"
        headingFontSize: "24px"
        # Custom CSS classes
        customCss: |
          .login-pf-page {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          }
          .card-pf {
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
          }
          .btn-primary {
            background: linear-gradient(45deg, #2563eb, #1d4ed8);
            border: none;
            border-radius: 6px;
            transition: all 0.3s ease;
          }
          .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
          }

  # Custom providers
  providers:
    # GitHub OAuth provider configuration
    github:
      providerId: "github"
      alias: "github"
      displayName: "GitHub"
      enabled: true
      updateProfileFirstLoginMode: "on"
      trustEmail: false
      storeToken: false
      addReadTokenRoleOnCreate: false
      authenticateByDefault: false
      linkOnly: false
      firstBrokerLoginFlowAlias: "first broker login"
      config:
        clientId: "${GITHUB_CLIENT_ID}"
        clientSecret: "${GITHUB_CLIENT_SECRET}"
        defaultScope: "user:email"
        syncMode: "IMPORT"
        useJwksUrl: "true"
        authorizationUrl: "https://github.com/login/oauth/authorize"
        tokenUrl: "https://github.com/login/oauth/access_token"
        userInfoUrl: "https://api.github.com/user"
      mappers:
        - name: "github-username"
          identityProviderMapper: "github-user-attribute-mapper"
          config:
            syncMode: "INHERIT"
            attribute: "login"
            user.attribute: "username"
        - name: "github-email"
          identityProviderMapper: "github-user-attribute-mapper"
          config:
            syncMode: "INHERIT"
            attribute: "email"
            user.attribute: "email"
        - name: "github-first-name"
          identityProviderMapper: "github-user-attribute-mapper"
          config:
            syncMode: "INHERIT"
            attribute: "name"
            user.attribute: "firstName"
        - name: "github-avatar"
          identityProviderMapper: "github-user-attribute-mapper"
          config:
            syncMode: "INHERIT"
            attribute: "avatar_url"
            user.attribute: "avatar"

    # Google OAuth provider configuration (disabled by default)
    google:
      providerId: "google"
      alias: "google"
      displayName: "Google"
      enabled: false
      updateProfileFirstLoginMode: "on"
      trustEmail: true
      storeToken: false
      addReadTokenRoleOnCreate: false
      authenticateByDefault: false
      linkOnly: false
      firstBrokerLoginFlowAlias: "first broker login"
      config:
        clientId: "${GOOGLE_CLIENT_ID}"
        clientSecret: "${GOOGLE_CLIENT_SECRET}"
        defaultScope: "openid email profile"
        syncMode: "IMPORT"
        useJwksUrl: "true"
        authorizationUrl: "https://accounts.google.com/o/oauth2/v2/auth"
        tokenUrl: "https://oauth2.googleapis.com/token"
        userInfoUrl: "https://openidconnect.googleapis.com/v1/userinfo"
      mappers:
        - name: "google-email"
          identityProviderMapper: "oidc-user-attribute-idp-mapper"
          config:
            syncMode: "INHERIT"
            claim: "email"
            user.attribute: "email"
        - name: "google-first-name"
          identityProviderMapper: "oidc-user-attribute-idp-mapper"
          config:
            syncMode: "INHERIT"
            claim: "given_name"
            user.attribute: "firstName"
        - name: "google-last-name"
          identityProviderMapper: "oidc-user-attribute-idp-mapper"
          config:
            syncMode: "INHERIT"
            claim: "family_name"
            user.attribute: "lastName"

# Network policy configuration
networkPolicy:
  enabled: true
  allowExternal: true
  additionalRules:
    ingress: []
    egress: []

# Monitoring
monitoring:
  enabled: true
  serviceMonitor:
    enabled: true
    namespace: "keycloak"  # ServiceMonitor will be created in the keycloak namespace
    interval: "30s"
    scrapeTimeout: "10s"
    labels:
      app: "keycloak"
      environment: "staging"

# Backup
backup:
  enabled: true
  schedule: "0 3 * * *"
  retention: "14d"
  storageClass: "standard"
  resources:
    limits:
      cpu: 250m
      memory: 256Mi
    requests:
      cpu: 100m
      memory: 128Mi
