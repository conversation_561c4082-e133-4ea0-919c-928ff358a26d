#!/usr/bin/env pwsh
# Test script for Keycloak configurations
# This script validates both development and production configurations

Write-Host "🧪 Testing Keycloak Configurations" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

$ErrorActionPreference = "Stop"
$chartPath = "./deploy/kubernetes/keycloak"

# Test 1: Helm Lint Validation
Write-Host "`n📋 Test 1: Helm Lint Validation" -ForegroundColor Yellow

Write-Host "  ✓ Testing base configuration..." -ForegroundColor Green
$lintResult = helm lint $chartPath 2>&1
if ($LASTEXITCODE -ne 0) {
    Write-Host "  ❌ Base configuration lint failed:" -ForegroundColor Red
    Write-Host $lintResult
    exit 1
}

Write-Host "  ✓ Testing development configuration..." -ForegroundColor Green
$lintResult = helm lint $chartPath -f "$chartPath/values-dev.yaml" 2>&1
if ($LASTEXITCODE -ne 0) {
    Write-Host "  ❌ Development configuration lint failed:" -ForegroundColor Red
    Write-Host $lintResult
    exit 1
}

Write-Host "  ✓ Testing production configuration..." -ForegroundColor Green
$lintResult = helm lint $chartPath -f "$chartPath/values-prod.yaml" 2>&1
if ($LASTEXITCODE -ne 0) {
    Write-Host "  ❌ Production configuration lint failed:" -ForegroundColor Red
    Write-Host $lintResult
    exit 1
}

Write-Host "  ✅ All lint tests passed!" -ForegroundColor Green

# Test 2: Template Rendering
Write-Host "`n🎨 Test 2: Template Rendering" -ForegroundColor Yellow

Write-Host "  ✓ Testing development template rendering..." -ForegroundColor Green
$devTemplate = helm template tourdecloud-keycloak-dev $chartPath -f "$chartPath/values-dev.yaml" --dry-run 2>&1
if ($LASTEXITCODE -ne 0) {
    Write-Host "  ❌ Development template rendering failed:" -ForegroundColor Red
    Write-Host $devTemplate
    exit 1
}

Write-Host "  ✓ Testing production template rendering..." -ForegroundColor Green
$prodTemplate = helm template tourdecloud-keycloak-prod $chartPath -f "$chartPath/values-prod.yaml" --dry-run 2>&1
if ($LASTEXITCODE -ne 0) {
    Write-Host "  ❌ Production template rendering failed:" -ForegroundColor Red
    Write-Host $prodTemplate
    exit 1
}

Write-Host "  ✅ All template rendering tests passed!" -ForegroundColor Green

# Test 3: Configuration Merging Analysis
Write-Host "`n🔄 Test 3: Configuration Merging Analysis" -ForegroundColor Yellow

Write-Host "  ✓ Extracting development ConfigMap..." -ForegroundColor Green
$devConfigMap = helm template tourdecloud-keycloak-dev $chartPath -f "$chartPath/values-dev.yaml" --show-only templates/configmap.yaml 2>&1
if ($LASTEXITCODE -ne 0) {
    Write-Host "  ❌ Failed to extract development ConfigMap:" -ForegroundColor Red
    Write-Host $devConfigMap
    exit 1
}

Write-Host "  ✓ Extracting production ConfigMap..." -ForegroundColor Green
$prodConfigMap = helm template tourdecloud-keycloak-prod $chartPath -f "$chartPath/values-prod.yaml" --show-only templates/configmap.yaml 2>&1
if ($LASTEXITCODE -ne 0) {
    Write-Host "  ❌ Failed to extract production ConfigMap:" -ForegroundColor Red
    Write-Host $prodConfigMap
    exit 1
}

# Validate that both contain the expected configurations
if ($devConfigMap -match "realms\.json" -and $devConfigMap -match "themes\.json" -and $devConfigMap -match "providers\.json") {
    Write-Host "  ✅ Development ConfigMap contains all expected configurations!" -ForegroundColor Green
} else {
    Write-Host "  ❌ Development ConfigMap missing expected configurations!" -ForegroundColor Red
    exit 1
}

if ($prodConfigMap -match "realms\.json" -and $prodConfigMap -match "themes\.json" -and $prodConfigMap -match "providers\.json") {
    Write-Host "  ✅ Production ConfigMap contains all expected configurations!" -ForegroundColor Green
} else {
    Write-Host "  ❌ Production ConfigMap missing expected configurations!" -ForegroundColor Red
    exit 1
}

# Test 4: Environment-Specific Overrides
Write-Host "`n🎯 Test 4: Environment-Specific Overrides" -ForegroundColor Yellow

# Check for development-specific realm
if ($devConfigMap -match "tourdecloud-dev") {
    Write-Host "  ✅ Development environment has dev-specific realm!" -ForegroundColor Green
} else {
    Write-Host "  ❌ Development environment missing dev-specific realm!" -ForegroundColor Red
    exit 1
}

# Check for production-specific settings
if ($prodConfigMap -match "auth\.tourdecloud\.com") {
    Write-Host "  ✅ Production environment has production URLs!" -ForegroundColor Green
} else {
    Write-Host "  ❌ Production environment missing production URLs!" -ForegroundColor Red
    exit 1
}

# Check for GitHub OAuth configuration
if ($devConfigMap -match "github-oauth-info\.txt" -and $prodConfigMap -match "github-oauth-info\.txt") {
    Write-Host "  ✅ Both environments have GitHub OAuth configuration!" -ForegroundColor Green
} else {
    Write-Host "  ❌ Missing GitHub OAuth configuration in one or both environments!" -ForegroundColor Red
    exit 1
}

Write-Host "`n🎉 All Tests Passed Successfully!" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green
Write-Host "✅ Helm lint validation: PASSED" -ForegroundColor Green
Write-Host "✅ Template rendering: PASSED" -ForegroundColor Green
Write-Host "✅ Configuration merging: PASSED" -ForegroundColor Green
Write-Host "✅ Environment overrides: PASSED" -ForegroundColor Green
Write-Host "`n🚀 Keycloak configurations are ready for deployment!" -ForegroundColor Cyan
